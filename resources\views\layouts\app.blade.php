<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Clean Lara') }}</title>
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>
<body class="font-sans antialiased bg-gray-50">
    <div class="min-h-screen flex">

        <!-- Sidebar (desktop) -->
        <aside class="hidden md:flex md:flex-col w-64 bg-white border-r">
            <div class="h-16 flex items-center px-4 border-b">
                <a href="{{ route('dashboard') }}" class="text-lg font-semibold text-gray-800">
                    {{ config('app.name', 'Clean Lara') }}
                </a>
            </div>

            <nav class="flex-1 overflow-y-auto p-4 space-y-1">
                <x-sidebar-link href="{{ route('dashboard') }}" :active="request()->routeIs('dashboard')">
                    Dashboard
                </x-sidebar-link>

                <x-sidebar-link href="{{ route('profile.edit') }}" :active="request()->routeIs('profile.*')">
                    Profile
                </x-sidebar-link>

                <form method="POST" action="{{ route('logout') }}" class="mt-2">
                    @csrf
                    <x-sidebar-link href="#" onclick="event.preventDefault(); this.closest('form').submit();">
                        Logout
                    </x-sidebar-link>
                </form>
            </nav>

            <div class="p-4 border-t">
                <div class="text-xs text-gray-500">Logged in as</div>
                <div class="text-sm text-gray-800">{{ Auth::user()?->name }}</div>
            </div>
        </aside>

        <!-- Mobile topbar -->
        <div class="flex flex-col w-full md:hidden">
            <div class="h-14 flex items-center justify-between px-4 bg-white border-b">
                <a href="{{ route('dashboard') }}" class="font-semibold text-gray-800">
                    {{ config('app.name', 'Clean Lara') }}
                </a>

                <button id="mobile-sidebar-toggle" class="p-2 rounded-md focus:outline-none focus:ring">
                    <svg class="w-6 h-6 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                </button>
            </div>

            <!-- Mobile collapsible sidebar (hidden by default) -->
            <div id="mobile-sidebar" class="hidden bg-white border-b">
                <nav class="p-4 space-y-1">
                    <x-sidebar-link href="{{ route('dashboard') }}" :active="request()->routeIs('dashboard')">Dashboard</x-sidebar-link>
                    <x-sidebar-link href="{{ route('profile.edit') }}" :active="request()->routeIs('profile.*')">Profile</x-sidebar-link>
                    <form method="POST" action="{{ route('logout') }}" class="mt-2">
                        @csrf
                        <x-sidebar-link href="#" onclick="event.preventDefault(); this.closest('form').submit();">Logout</x-sidebar-link>
                    </form>
                </nav>
            </div>
        </div>

        <!-- Main content -->
        <main class="flex-1">
            <div class="p-6">
                @if (isset($header))
                    <header class="mb-6">
                        {{ $header }}
                    </header>
                @endif

                <div class="bg-white shadow-sm rounded-lg p-6">
                    {{ $slot }}
                </div>
            </div>
        </main>
    </div>

    <script>
        // Small mobile toggle
        document.addEventListener('DOMContentLoaded', function () {
            const btn = document.getElementById('mobile-sidebar-toggle');
            const sidebar = document.getElementById('mobile-sidebar');
            btn?.addEventListener('click', () => {
                if (!sidebar) return;
                sidebar.classList.toggle('hidden');
            });
        });
    </script>
</body>
</html>
